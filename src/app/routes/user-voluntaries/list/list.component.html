<page-header [action]="phActionTpl">
  <ng-template #phActionTpl>
    <button (click)="add()"
            [acl-ability]="'voluntaries.create'" acl
            nz-button nzType="primary">أضف
    </button>
  </ng-template>
</page-header>
<nz-card>
  <!--<sf mode="search" [schema]="searchSchema" (formSubmit)="st.reset($event)" (formReset)="st.reset($event)"></sf>-->
  <st #st (change)="updateUrlParameters($event)" [columns]="columns" [data]="url" [pi]="page"
      [ps]="length" [req]="{ params: buildFilters() }"></st>
</nz-card>
