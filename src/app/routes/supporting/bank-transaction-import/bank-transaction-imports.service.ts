import {Injectable} from '@angular/core';
import {_HttpClient} from '@delon/theme';
import {objectToFormData} from "../../../shared/utils/helper.functions";

@Injectable({providedIn: 'root'})
export class BankTransactionImportsService {
  constructor(private http: _HttpClient) {
  }

  store(data: any) {
    return this.http.post(`/v1/bank-transaction-imports`, objectToFormData(data))
  }

  view(uuid: string, data: any) {
    return this.http.get(`/v1/bank-transaction-imports/${uuid}`, data)
  }

  update(uuid: string, data: any) {
    return this.http.put(`/v1/bank-transaction-imports/${uuid}`, data)
  }

  actions(uuid: string, action: string) {
    return this.http.post(`/v1/bank-transaction-imports/${uuid}/actions`, {
      action
    })
  }
}
