import {Component, Input, numberAttribute, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {STColumn, STComponent} from '@delon/abc/st';
import {<PERSON>dal<PERSON>elper, _HttpClient} from '@delon/theme';
import {NzMessageService} from "ng-zorro-antd/message";
import {FamilyGraphSharesEditComponent} from "../edit/edit.component";
import {FamilyGraphSharesCreateComponent} from "../create/create.component";
import {Subject, takeUntil} from "rxjs";
import {NzSafeAny} from "ng-zorro-antd/core/types";
import {NzDrawerService} from "ng-zorro-antd/drawer";
import {ActivatedRoute, NavigationExtras, Router} from "@angular/router";
import {Location} from "@angular/common";
import {Clipboard} from "@angular/cdk/clipboard";
import {FamilyGraphSharesService} from "../family-graph-shares.service";
import {
  buildDataTablesQuery, notEmpty, oFilter,
  pageAttribute,
  perPageAttribute,
  filtersAttribute
} from "../../../shared/utils/helper.functions";

@Component({
  selector: 'app-family-graph-shares-list',
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.less'],
})
export class FamilyGraphSharesListComponent implements OnInit, OnDestroy {
  @Input({transform: numberAttribute, alias: 'id'}) familyGraphShareId?: number;
  @Input({transform: pageAttribute}) page: number = 1;
  @Input({transform: perPageAttribute}) length: number = 10;
  @Input({transform: filtersAttribute}) filters!: NzSafeAny;
  destroyed$ = new Subject<void>();
  url = `/v1/family-graph-shares`;
  @ViewChild('st') private readonly st!: STComponent;
  columns: STColumn[] = [
    {title: '#', index: 'DT_RowIndex'},
    {title: {i18n: 'family-graph-share.fields.user_name'}, index: 'user_name'},
    {title: {i18n: 'family-graph-share.data.views'}, index: 'views', type: 'number'},
    {title: {i18n: 'family-graph-share.data.last_view_at'}, index: 'last_view_at', type: 'date', dateFormat: 'yyyy-MM-dd hh:mm aa'},
    //{title: {i18n: 'family-graph-share.fields.expired_at'}, index: 'expired_at', type: 'date', dateFormat: 'yyyy-MM-dd hh:mm aa'},
    {title: {i18n: 'common.created_at'}, index: 'created_at', type: 'date', dateFormat: 'yyyy-MM-dd hh:mm aa'},
    {
      title: '',
      buttons: [
        {
          icon: 'copy',
          click: (record, _modal, comp) => {
            this.clipboard.copy(record.share_url);
            this.message.success('تم نسخ الرابط')
          }
        },
        {
          icon: 'edit',
          //acl: {ability: ['events.update']},
          iif: (item) => !item.deleted_at,
          click: (record, _modal, comp) => {
            return this.edit(record.id)
          }
        },
        {
          icon: 'delete',
          tooltip: 'حذف',
          pop: 'تأكيد حذف المشجرة ؟',
          acl: {ability: ['child-forms.delete']},
          iif: (item) => !item.status,
          click: (record, _modal, comp) => {
            this.familyGraphSharesService.delete(record.id)
              .subscribe(res => {
                this.reloadFilters(this.st.pi);
              });
          },
        }
      ]
    }
  ];

  constructor(private http: _HttpClient,
              private drawerService: NzDrawerService,
              private familyGraphSharesService: FamilyGraphSharesService,
              private router: Router,
              private route: ActivatedRoute,
              private location: Location,
              private clipboard: Clipboard,
              private message: NzMessageService) {
  }

  ngOnInit(): void {
    this.route.data.subscribe(data => {
      switch (data['action']) {
        case 'create':
          this.add()
          break;
        case 'edit':
          if (this.familyGraphShareId)
            this.edit(this.familyGraphShareId)
          break;
      }
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  resetFilters(withoutReload = false) {
    this.filters = {};
    if (this.st && !withoutReload)
    this.st.reset({});
  }

  reloadFilters(pi = 1) {
    this.st.load(pi, {}, {
      merge: false,
      toTop: false,
    });
  }

  add(): void {
    this.location.go('/family-graph-shares/create');
    const drawerRef = this.drawerService.create<FamilyGraphSharesCreateComponent, { user: any }, any>({
      nzTitle: 'إضافة جديد',
      nzContent: FamilyGraphSharesCreateComponent,
      nzPlacement: 'left',
      nzClosable: false,
      nzContentParams: {}
    });
    drawerRef.afterClose.pipe(takeUntil(this.destroyed$)).subscribe((data: any) => {
      // if (this.route.routeConfig?.data!['action'] !== 'index')
      this.location.go('/family-graph-shares', this.buildQueryParams());
      this.reloadFilters(this.st.pi);
    });
  }

  edit(familyGraphShareId: number): void {
    this.location.go(`/family-graph-shares/${familyGraphShareId}/edit`);
    const editDrawerRef = this.drawerService.create<FamilyGraphSharesEditComponent, { familyGraphShareId: any }, any>({
      nzTitle: 'تعديل',
      nzContent: FamilyGraphSharesEditComponent,
      nzPlacement: 'left',
      nzClosable: false,
      nzContentParams: {
        familyGraphShareId: familyGraphShareId
      }
    });
    editDrawerRef.afterClose.pipe(takeUntil(this.destroyed$)).subscribe((data: any) => {
      // if (this.route.routeConfig?.data!['action'] !== 'index')
      this.location.go('/family-graph-shares', this.buildQueryParams());
      this.reloadFilters(this.st.pi);
    });
  }

  buildFilters() {
    return buildDataTablesQuery({})
  }

  buildQueryArray(pi: number | undefined = undefined, ps: number | undefined = undefined): any {
    let query: any = oFilter({
      page: pi ?? this.st.pi,
      length: ps ?? this.st.ps,
      filters: JSON.stringify(oFilter(this.filters, (v, k) => notEmpty(v))),
    }, (v, k) => v && v !== '{}');
    query.filters = query.filters || null;
    return query;
  }

  buildQueryParams() {
    return new URLSearchParams(this.buildQueryArray()).toString();
  }

  updateUrlParameters(event: any) {
    event.preventDefault();
    event.stopPropagation();
    const navigationExtras: NavigationExtras = {
      queryParams: this.buildQueryArray(event.pi, event.ps),
      queryParamsHandling: 'merge'
    };
    this.router.navigate([], navigationExtras)
  }
}
